import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:mides_skadik/app/data/utils/file_download.dart';
import 'package:mides_skadik/widgets/components/custom_snackbar.dart';
import 'package:mides_skadik/app/data/utils/log_service.dart';
import 'package:mides_skadik/app/data/utils/file_picker.dart';
import 'package:file_picker/file_picker.dart';
import 'dart:io';
import 'dart:convert';

class VideoEditorView extends StatefulWidget {
  const VideoEditorView({super.key});

  @override
  State<VideoEditorView> createState() => _VideoEditorViewState();
}

class _VideoEditorViewState extends State<VideoEditorView> {
  late final WebViewController _webViewController;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _initializeWebView();
  }

  void _initializeWebView() {
    _webViewController = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setBackgroundColor(Colors.white)
      ..setNavigationDelegate(
        NavigationDelegate(
          onProgress: (int progress) {
            // Update loading progress if needed
          },
          onPageStarted: (String url) {
            setState(() {
              _isLoading = true;
            });
          },
          onPageFinished: (String url) {
            setState(() {
              _isLoading = false;
            });
            _injectDownloadScript();
          },
          onHttpError: (HttpResponseError error) {
            // Handle HTTP errors
            LogService.log.e('HTTP Error: ${error.response?.statusCode}');
          },
          onWebResourceError: (WebResourceError error) {
            // Handle web resource errors
            LogService.log.e('Web Resource Error: ${error.description}');
          },
        ),
      )
      ..addJavaScriptChannel(
        'MediaDownloader',
        onMessageReceived: (JavaScriptMessage message) {
          _handleDownloadRequest(message.message);
        },
      )
      ..addJavaScriptChannel(
        'MediaPicker',
        onMessageReceived: (JavaScriptMessage message) {
          _handleMediaPickerRequest(message.message);
        },
      )
      ..loadRequest(Uri.parse('http://***************:3000/'));
  }

  void _injectDownloadScript() {
    const script = '''
      (function() {
        // Function to detect and handle media files
        function setupMediaDownloader() {
          // Find all video and audio elements
          const mediaElements = document.querySelectorAll('video, audio');

          mediaElements.forEach(function(element) {
            // Add download button for each media element
            if (!element.hasAttribute('data-download-added')) {
              element.setAttribute('data-download-added', 'true');

              // Create download button
              const downloadBtn = document.createElement('button');
              downloadBtn.innerHTML = '📥 Download';
              downloadBtn.style.cssText = `
                position: absolute;
                top: 10px;
                right: 10px;
                z-index: 9999;
                background: rgba(0,0,0,0.7);
                color: white;
                border: none;
                padding: 8px 12px;
                border-radius: 4px;
                cursor: pointer;
                font-size: 12px;
              `;

              // Add click handler
              downloadBtn.onclick = function(e) {
                e.preventDefault();
                e.stopPropagation();

                const src = element.src || element.currentSrc;
                if (src) {
                  const fileName = src.split('/').pop() || 'media_file';
                  const fileType = element.tagName.toLowerCase(); // 'video' or 'audio'

                  // Send download request to Flutter
                  window.MediaDownloader.postMessage(JSON.stringify({
                    url: src,
                    fileName: fileName,
                    type: fileType
                  }));
                }
              };

              // Position the element relatively and add the button
              const wrapper = document.createElement('div');
              wrapper.style.position = 'relative';
              wrapper.style.display = 'inline-block';

              element.parentNode.insertBefore(wrapper, element);
              wrapper.appendChild(element);
              wrapper.appendChild(downloadBtn);
            }
          });

          // Also handle direct links to media files
          const links = document.querySelectorAll('a[href]');
          links.forEach(function(link) {
            const href = link.href;
            if (href.match(/\\.(mp4|avi|mov|wmv|flv|webm|mp3|wav|ogg|aac|m4a)\$/i)) {
              if (!link.hasAttribute('data-download-added')) {
                link.setAttribute('data-download-added', 'true');

                // Add download option to context menu or click handler
                link.addEventListener('contextmenu', function(e) {
                  e.preventDefault();

                  const fileName = href.split('/').pop() || 'media_file';
                  const fileType = href.match(/\\.(mp4|avi|mov|wmv|flv|webm)\$/i) ? 'video' : 'audio';

                  window.MediaDownloader.postMessage(JSON.stringify({
                    url: href,
                    fileName: fileName,
                    type: fileType
                  }));
                });
              }
            }
          });
        }

        // Function to setup media picker for "Add media" buttons
        function setupMediaPicker() {
          console.log('🔍 Setting up media picker...');

          // Look for various types of upload/media buttons and inputs
          const selectors = [
            'button',
            '[role="button"]',
            'input[type="file"]',
            '.upload-btn',
            '.add-media',
            '.file-upload',
            '.media-upload',
            '.btn-upload',
            '[data-testid*="upload"]',
            '[data-testid*="file"]',
            '[data-testid*="media"]',
            '[class*="upload"]',
            '[class*="file"]',
            '[class*="media"]',
            '[id*="upload"]',
            '[id*="file"]',
            '[id*="media"]'
          ];

          const elements = document.querySelectorAll(selectors.join(', '));
          console.log('📋 Found ' + elements.length + ' potential elements');

          elements.forEach(function(element, index) {
            if (element.hasAttribute('data-picker-added')) return;

            const text = (element.textContent || element.innerText || element.value || '').toLowerCase().trim();
            const className = (element.className || '').toLowerCase();
            const id = (element.id || '').toLowerCase();
            const dataTestId = (element.getAttribute('data-testid') || '').toLowerCase();
            const ariaLabel = (element.getAttribute('aria-label') || '').toLowerCase();
            const title = (element.getAttribute('title') || '').toLowerCase();

            console.log('🔍 Checking element ' + index + ':', {
              tag: element.tagName,
              text: text,
              className: className,
              id: id,
              dataTestId: dataTestId,
              ariaLabel: ariaLabel,
              title: title
            });

            // More comprehensive detection
            const isMediaButton =
              text.includes('add media') ||
              text.includes('add file') ||
              text.includes('upload') ||
              text.includes('choose file') ||
              text.includes('select file') ||
              text.includes('browse') ||
              text.includes('attach') ||
              text.includes('import') ||
              text.includes('media') ||
              className.includes('upload') ||
              className.includes('file') ||
              className.includes('media') ||
              className.includes('import') ||
              id.includes('upload') ||
              id.includes('file') ||
              id.includes('media') ||
              id.includes('import') ||
              dataTestId.includes('upload') ||
              dataTestId.includes('file') ||
              dataTestId.includes('media') ||
              dataTestId.includes('import') ||
              ariaLabel.includes('upload') ||
              ariaLabel.includes('file') ||
              ariaLabel.includes('media') ||
              title.includes('upload') ||
              title.includes('file') ||
              title.includes('media') ||
              element.tagName.toLowerCase() === 'input' && element.type === 'file';

            if (isMediaButton) {
              element.setAttribute('data-picker-added', 'true');

              console.log('✅ Found media button:', element);

              // Store original handlers
              const originalClick = element.onclick;

              // For file inputs, override the click
              if (element.tagName.toLowerCase() === 'input' && element.type === 'file') {
                element.addEventListener('click', function(e) {
                  e.preventDefault();
                  e.stopPropagation();

                  console.log('📁 File input clicked, opening Flutter picker');

                  window.MediaPicker.postMessage(JSON.stringify({
                    action: 'pick_media',
                    elementType: 'file_input',
                    accept: element.accept || '*/*',
                    multiple: element.multiple || false
                  }));
                }, true);
              } else {
                // For buttons and other elements
                element.addEventListener('click', function(e) {
                  e.preventDefault();
                  e.stopPropagation();

                  console.log('🎯 Media button clicked, opening Flutter picker');

                  window.MediaPicker.postMessage(JSON.stringify({
                    action: 'pick_media',
                    elementType: 'button',
                    buttonText: text,
                    buttonId: id,
                    buttonClass: className
                  }));
                }, true);
              }

              // Also add visual indicator
              element.style.border = '2px dashed #007bff';
              element.style.position = 'relative';

              // Add a small indicator
              const indicator = document.createElement('div');
              indicator.innerHTML = '📱';
              indicator.style.cssText = `
                position: absolute;
                top: -5px;
                right: -5px;
                background: #007bff;
                color: white;
                border-radius: 50%;
                width: 20px;
                height: 20px;
                font-size: 10px;
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 9999;
              `;
              element.style.position = 'relative';
              element.appendChild(indicator);
            }
          });

          // Also look for drag and drop areas
          const dropSelectors = [
            '[data-testid*="drop"]',
            '.drop-zone',
            '.upload-area',
            '.file-drop',
            '.dropzone',
            '[class*="drop"]',
            '[class*="drag"]'
          ];

          const dropAreas = document.querySelectorAll(dropSelectors.join(', '));
          console.log('📦 Found ' + dropAreas.length + ' drop areas');

          dropAreas.forEach(function(area) {
            if (!area.hasAttribute('data-drop-added')) {
              area.setAttribute('data-drop-added', 'true');

              area.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                console.log('📦 Drop area clicked, opening Flutter picker');

                window.MediaPicker.postMessage(JSON.stringify({
                  action: 'pick_media',
                  elementType: 'drop_area'
                }));
              });

              // Add visual indicator for drop areas
              area.style.border = '2px dashed #28a745';
            }
          });
        }

        // Run initially
        console.log('🚀 Initial setup...');
        setupMediaDownloader();
        setupMediaPicker();

        // Run when new content is loaded (for dynamic content)
        const observer = new MutationObserver(function(mutations) {
          let shouldRerun = false;
          mutations.forEach(function(mutation) {
            if (mutation.addedNodes.length > 0) {
              // Check if any of the added nodes are relevant
              mutation.addedNodes.forEach(function(node) {
                if (node.nodeType === Node.ELEMENT_NODE) {
                  const element = node;
                  const hasRelevantContent =
                    element.tagName === 'BUTTON' ||
                    element.tagName === 'INPUT' ||
                    element.querySelector && (
                      element.querySelector('button') ||
                      element.querySelector('input[type="file"]') ||
                      element.querySelector('[class*="upload"]') ||
                      element.querySelector('[class*="media"]')
                    );

                  if (hasRelevantContent) {
                    shouldRerun = true;
                    console.log('🔄 DOM changed, relevant content added:', element);
                  }
                }
              });
            }
          });

          if (shouldRerun) {
            console.log('🔄 Re-running setup due to DOM changes...');
            setTimeout(() => {
              setupMediaDownloader();
              setupMediaPicker();
            }, 100);
          }
        });

        observer.observe(document.body, {
          childList: true,
          subtree: true,
          attributes: true,
          attributeFilter: ['class', 'id', 'data-testid']
        });

        // Also run setup periodically to catch any missed elements
        setInterval(function() {
          console.log('⏰ Periodic setup check...');
          setupMediaDownloader();
          setupMediaPicker();
        }, 5000);

        // Add global debugging function
        window.debugMediaPicker = function() {
          console.log('🐛 Debug info:');
          console.log('File inputs:', document.querySelectorAll('input[type="file"]'));
          console.log('Buttons:', document.querySelectorAll('button'));
          console.log('Upload elements:', document.querySelectorAll('[class*="upload"], [id*="upload"]'));
          console.log('Media elements:', document.querySelectorAll('[class*="media"], [id*="media"]'));
        };

        console.log('✅ Media picker setup completed. Use debugMediaPicker() for debugging.');
      })();
    ''';

    _webViewController.runJavaScript(script);
  }

  void _handleDownloadRequest(String message) async {
    try {
      LogService.log.i('Download request received: $message');

      // Parse JSON message from JavaScript
      if (message.contains('{') && message.contains('}')) {
        // Simple JSON parsing for the expected format
        final urlMatch = RegExp(r'"url":"([^"]*)"').firstMatch(message);
        final fileNameMatch =
            RegExp(r'"fileName":"([^"]*)"').firstMatch(message);
        final typeMatch = RegExp(r'"type":"([^"]*)"').firstMatch(message);

        if (urlMatch != null) {
          final url = urlMatch.group(1) ?? '';
          final fileName = fileNameMatch?.group(1) ?? 'media_file';
          final fileType = typeMatch?.group(1) ?? 'unknown';

          await _downloadFile(url, fileName, fileType);
        }
      }
    } catch (e) {
      LogService.log.e('Error handling download: $e');
      if (mounted) {
        SnackbarUtil.showOnce(
          title: "Error",
          message: "Failed to download file",
        );
      }
    }
  }

  void _handleMediaPickerRequest(String message) async {
    try {
      LogService.log.i('Media picker request received: $message');

      // Parse JSON message from JavaScript
      if (message.contains('pick_media')) {
        await _showMediaPickerDialog();
      } else if (message.contains('upload_complete')) {
        // Handle upload completion notification
        try {
          final data = message.contains('{') && message.contains('}')
              ? message
              : '{"action":"upload_complete","success":false}';

          final successMatch =
              RegExp(r'"success":\s*(true|false)').firstMatch(data);
          final fileNameMatch =
              RegExp(r'"fileName":"([^"]*)"').firstMatch(data);
          final errorMatch = RegExp(r'"error":"([^"]*)"').firstMatch(data);

          final success = successMatch?.group(1) == 'true';
          final fileName = fileNameMatch?.group(1) ?? 'file';
          final error = errorMatch?.group(1);

          if (mounted) {
            if (success) {
              SnackbarUtil.showOnce(
                title: "Success",
                message: "File $fileName uploaded successfully to web editor",
              );
            } else {
              SnackbarUtil.showOnce(
                title: "Upload Status",
                message: error != null
                    ? "Upload issue: $error"
                    : "File $fileName processed, please check web editor",
              );
            }
          }
        } catch (e) {
          LogService.log.e('Error parsing upload completion: $e');
        }
      }
    } catch (e) {
      LogService.log.e('Error handling media picker: $e');
      if (mounted) {
        SnackbarUtil.showOnce(
          title: "Error",
          message: "Failed to open media picker",
        );
      }
    }
  }

  Future<void> _showMediaPickerDialog() async {
    if (!mounted) return;

    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return Container(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                'Select Media Type',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 20),
              ListTile(
                leading: const Icon(Icons.video_library),
                title: const Text('Pick Video'),
                onTap: () async {
                  Navigator.pop(context);
                  await _pickVideo();
                },
              ),
              ListTile(
                leading: const Icon(Icons.photo_library),
                title: const Text('Pick Image'),
                onTap: () async {
                  Navigator.pop(context);
                  await _pickImage();
                },
              ),
              ListTile(
                leading: const Icon(Icons.audiotrack),
                title: const Text('Pick Audio'),
                onTap: () async {
                  Navigator.pop(context);
                  await _pickAudio();
                },
              ),
              ListTile(
                leading: const Icon(Icons.file_present),
                title: const Text('Pick Any File'),
                onTap: () async {
                  Navigator.pop(context);
                  await _pickAnyFile();
                },
              ),
            ],
          ),
        );
      },
    );
  }

  Future<void> _pickVideo() async {
    try {
      final String? path = await FilePickerUtil.pickVideo();
      if (path != null) {
        await _uploadFileToWebView(path, 'video');
      }
    } catch (e) {
      LogService.log.e('Error picking video: $e');
      if (mounted) {
        SnackbarUtil.showOnce(
          title: "Error",
          message: "Failed to pick video",
        );
      }
    }
  }

  Future<void> _pickImage() async {
    try {
      final String? path = await FilePickerUtil.pickImage();
      if (path != null) {
        await _uploadFileToWebView(path, 'image');
      }
    } catch (e) {
      LogService.log.e('Error picking image: $e');
      if (mounted) {
        SnackbarUtil.showOnce(
          title: "Error",
          message: "Failed to pick image",
        );
      }
    }
  }

  Future<void> _pickAudio() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.audio,
        allowMultiple: false,
      );

      if (result != null && result.files.isNotEmpty) {
        final path = result.files.first.path;
        if (path != null) {
          await _uploadFileToWebView(path, 'audio');
        }
      }
    } catch (e) {
      LogService.log.e('Error picking audio: $e');
      if (mounted) {
        SnackbarUtil.showOnce(
          title: "Error",
          message: "Failed to pick audio",
        );
      }
    }
  }

  Future<void> _pickAnyFile() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        allowMultiple: false,
      );

      if (result != null && result.files.isNotEmpty) {
        final path = result.files.first.path;
        if (path != null) {
          await _uploadFileToWebView(path, 'file');
        }
      }
    } catch (e) {
      LogService.log.e('Error picking file: $e');
      if (mounted) {
        SnackbarUtil.showOnce(
          title: "Error",
          message: "Failed to pick file",
        );
      }
    }
  }

  Future<void> _uploadFileToWebView(String filePath, String fileType) async {
    try {
      LogService.log.i('Uploading file to WebView: $filePath');

      // Read file as base64
      final file = File(filePath);
      final bytes = await file.readAsBytes();
      final base64String = base64Encode(bytes);
      final fileName = file.path.split('/').last;
      final mimeType = _getMimeType(fileName);

      // Create data URL
      final dataUrl = 'data:$mimeType;base64,$base64String';

      // Inject JavaScript to handle the file upload
      final script = '''
        (function() {
          try {
            console.log('🚀 Starting file upload process...');

            // Create a file object from the data URL
            const fileName = '$fileName';
            const fileType = '$fileType';
            const dataUrl = '$dataUrl';
            const mimeType = '$mimeType';

            console.log('📁 File details:', { fileName, fileType, mimeType });

            // Convert data URL to blob
            fetch(dataUrl)
              .then(res => res.blob())
              .then(blob => {
                console.log('📦 Blob created:', blob);

                // Create a File object
                const file = new File([blob], fileName, { type: mimeType });
                console.log('📄 File object created:', file);

                // Store file globally for debugging
                window._uploadedFile = file;

                // Method 1: Try to find file input elements and set the file
                const fileInputs = document.querySelectorAll('input[type="file"]');
                console.log('🔍 Found ' + fileInputs.length + ' file inputs');

                if (fileInputs.length > 0) {
                  fileInputs.forEach((input, index) => {
                    try {
                      console.log('📝 Processing file input ' + index + ':', input);

                      // Create a DataTransfer object to simulate file selection
                      const dt = new DataTransfer();
                      dt.items.add(file);

                      // Set the files property
                      input.files = dt.files;

                      // Trigger multiple events to ensure compatibility
                      const events = ['input', 'change', 'blur'];
                      events.forEach(eventType => {
                        const event = new Event(eventType, { bubbles: true, cancelable: true });
                        input.dispatchEvent(event);
                      });

                      console.log('✅ File input ' + index + ' updated');
                    } catch (error) {
                      console.error('❌ Error updating file input ' + index + ':', error);
                    }
                  });
                }

                // Method 2: Try to trigger drag and drop handlers
                const dropSelectors = [
                  '[data-testid*="drop"]',
                  '.drop-zone',
                  '.upload-area',
                  '.dropzone',
                  '[class*="drop"]',
                  '[class*="drag"]'
                ];

                const dropZones = document.querySelectorAll(dropSelectors.join(', '));
                console.log('📦 Found ' + dropZones.length + ' drop zones');

                if (dropZones.length > 0) {
                  dropZones.forEach((zone, index) => {
                    try {
                      console.log('📦 Processing drop zone ' + index + ':', zone);

                      const dt = new DataTransfer();
                      dt.items.add(file);

                      // Trigger drag events sequence
                      const dragEvents = ['dragenter', 'dragover', 'drop'];
                      dragEvents.forEach(eventType => {
                        const event = new DragEvent(eventType, {
                          bubbles: true,
                          cancelable: true,
                          dataTransfer: dt
                        });
                        zone.dispatchEvent(event);
                      });

                      console.log('✅ Drop zone ' + index + ' triggered');
                    } catch (error) {
                      console.error('❌ Error triggering drop zone ' + index + ':', error);
                    }
                  });
                }

                // Method 3: Try to find upload buttons and store file data
                const uploadSelectors = [
                  'button',
                  '[role="button"]',
                  '.upload-btn',
                  '.btn-upload',
                  '[class*="upload"]'
                ];

                const uploadButtons = document.querySelectorAll(uploadSelectors.join(', '));
                console.log('🎯 Found ' + uploadButtons.length + ' upload buttons');

                uploadButtons.forEach((button, index) => {
                  const text = (button.textContent || button.innerText || '').toLowerCase();
                  if (text.includes('upload') || text.includes('add') || text.includes('choose') || text.includes('import')) {
                    // Store file data on the button for later use
                    button._selectedFile = file;
                    button._selectedFiles = [file];

                    console.log('💾 File stored on button ' + index + ':', button);
                  }
                });

                // Method 4: Try to find and trigger any custom upload handlers
                // Look for elements with upload-related event listeners
                const allElements = document.querySelectorAll('*');
                let uploadHandlers = 0;

                allElements.forEach(element => {
                  // Check if element has upload-related attributes or classes
                  const hasUploadAttr = element.hasAttribute('data-upload') ||
                                       element.hasAttribute('data-file') ||
                                       element.className.includes('upload') ||
                                       element.className.includes('file');

                  if (hasUploadAttr) {
                    element._selectedFile = file;
                    uploadHandlers++;
                  }
                });

                console.log('🔧 Found ' + uploadHandlers + ' elements with upload attributes');

                // Method 5: Try to trigger any global upload functions
                if (typeof window.handleFileUpload === 'function') {
                  console.log('🌐 Calling global handleFileUpload function');
                  window.handleFileUpload(file);
                }

                if (typeof window.onFileSelected === 'function') {
                  console.log('🌐 Calling global onFileSelected function');
                  window.onFileSelected(file);
                }

                console.log('✅ File upload process completed successfully:', fileName);

                // Notify Flutter about success
                window.MediaPicker.postMessage(JSON.stringify({
                  action: 'upload_complete',
                  fileName: fileName,
                  fileType: fileType,
                  success: true
                }));
              })
              .catch(error => {
                console.error('❌ Error uploading file:', error);

                // Notify Flutter about error
                window.MediaPicker.postMessage(JSON.stringify({
                  action: 'upload_complete',
                  fileName: fileName,
                  fileType: fileType,
                  success: false,
                  error: error.message
                }));
              });
          } catch (error) {
            console.error('❌ Error in upload script:', error);

            // Notify Flutter about error
            window.MediaPicker.postMessage(JSON.stringify({
              action: 'upload_complete',
              fileName: '$fileName',
              fileType: '$fileType',
              success: false,
              error: error.message
            }));
          }
        })();
      ''';

      await _webViewController.runJavaScript(script);

      if (mounted) {
        SnackbarUtil.showOnce(
          title: "Processing",
          message: "File $fileName is being uploaded...",
        );
      }
    } catch (e) {
      LogService.log.e('Error uploading file to WebView: $e');
      if (mounted) {
        SnackbarUtil.showOnce(
          title: "Error",
          message: "Failed to upload file",
        );
      }
    }
  }

  String _getMimeType(String fileName) {
    final extension = fileName.split('.').last.toLowerCase();
    switch (extension) {
      case 'mp4':
        return 'video/mp4';
      case 'avi':
        return 'video/avi';
      case 'mov':
        return 'video/quicktime';
      case 'wmv':
        return 'video/x-ms-wmv';
      case 'flv':
        return 'video/x-flv';
      case 'webm':
        return 'video/webm';
      case 'mp3':
        return 'audio/mpeg';
      case 'wav':
        return 'audio/wav';
      case 'ogg':
        return 'audio/ogg';
      case 'aac':
        return 'audio/aac';
      case 'm4a':
        return 'audio/mp4';
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      case 'png':
        return 'image/png';
      case 'gif':
        return 'image/gif';
      case 'webp':
        return 'image/webp';
      case 'pdf':
        return 'application/pdf';
      case 'doc':
        return 'application/msword';
      case 'docx':
        return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
      default:
        return 'application/octet-stream';
    }
  }

  Future<void> _downloadFile(
      String url, String fileName, String fileType) async {
    try {
      // Request storage permission
      final permission = await Permission.storage.request();
      if (permission.isGranted || await Permission.photos.request().isGranted) {
        // Show loading indicator
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Downloading file...'),
              duration: Duration(seconds: 2),
            ),
          );
        }

        // Download the file
        await FileDownloadUtil.downloadFile(
          url,
          fileName: fileName,
        );

        if (mounted) {
          SnackbarUtil.showOnce(
            title: "Success",
            message: "File $fileType downloaded to gallery",
          );
        }
      } else {
        if (mounted) {
          SnackbarUtil.showOnce(
            title: "Permission Required",
            message: "Storage permission is required to download files",
          );
        }
      }
    } catch (e) {
      LogService.log.e('Error downloading file: $e');
      if (mounted) {
        SnackbarUtil.showOnce(
          title: "Error",
          message: "Failed to download file",
        );
      }
    }
  }

  @override
  void dispose() {
    // Clean up WebView resources
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Video Editor'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              _webViewController.reload();
            },
          ),
        ],
      ),
      body: Stack(
        children: [
          WebViewWidget(controller: _webViewController),
          if (_isLoading)
            const Center(
              child: CircularProgressIndicator(),
            ),
        ],
      ),
    );
  }
}
