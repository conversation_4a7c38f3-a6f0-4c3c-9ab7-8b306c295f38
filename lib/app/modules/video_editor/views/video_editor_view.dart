import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:mides_skadik/app/data/utils/file_download.dart';
import 'package:mides_skadik/widgets/components/custom_snackbar.dart';
import 'package:mides_skadik/app/data/utils/log_service.dart';
import 'package:mides_skadik/app/data/utils/file_picker.dart';
import 'package:file_picker/file_picker.dart';
import 'dart:io';
import 'dart:convert';

class VideoEditorView extends StatefulWidget {
  const VideoEditorView({super.key});

  @override
  State<VideoEditorView> createState() => _VideoEditorViewState();
}

class _VideoEditorViewState extends State<VideoEditorView> {
  late final WebViewController _webViewController;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _initializeWebView();
  }

  void _initializeWebView() {
    _webViewController = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setBackgroundColor(Colors.white)
      ..setNavigationDelegate(
        NavigationDelegate(
          onProgress: (int progress) {
            // Update loading progress if needed
          },
          onPageStarted: (String url) {
            setState(() {
              _isLoading = true;
            });
          },
          onPageFinished: (String url) {
            setState(() {
              _isLoading = false;
            });
            _injectDownloadScript();
          },
          onHttpError: (HttpResponseError error) {
            // Handle HTTP errors
            LogService.log.e('HTTP Error: ${error.response?.statusCode}');
          },
          onWebResourceError: (WebResourceError error) {
            // Handle web resource errors
            LogService.log.e('Web Resource Error: ${error.description}');
          },
        ),
      )
      ..addJavaScriptChannel(
        'MediaDownloader',
        onMessageReceived: (JavaScriptMessage message) {
          _handleDownloadRequest(message.message);
        },
      )
      ..addJavaScriptChannel(
        'MediaPicker',
        onMessageReceived: (JavaScriptMessage message) {
          _handleMediaPickerRequest(message.message);
        },
      )
      ..loadRequest(Uri.parse('http://***************:3000/'));
  }

  void _injectDownloadScript() {
    const script = '''
      (function() {
        // Function to detect and handle media files
        function setupMediaDownloader() {
          // Find all video and audio elements
          const mediaElements = document.querySelectorAll('video, audio');

          mediaElements.forEach(function(element) {
            // Add download button for each media element
            if (!element.hasAttribute('data-download-added')) {
              element.setAttribute('data-download-added', 'true');

              // Create download button
              const downloadBtn = document.createElement('button');
              downloadBtn.innerHTML = '📥 Download';
              downloadBtn.style.cssText = `
                position: absolute;
                top: 10px;
                right: 10px;
                z-index: 9999;
                background: rgba(0,0,0,0.7);
                color: white;
                border: none;
                padding: 8px 12px;
                border-radius: 4px;
                cursor: pointer;
                font-size: 12px;
              `;

              // Add click handler
              downloadBtn.onclick = function(e) {
                e.preventDefault();
                e.stopPropagation();

                const src = element.src || element.currentSrc;
                if (src) {
                  const fileName = src.split('/').pop() || 'media_file';
                  const fileType = element.tagName.toLowerCase(); // 'video' or 'audio'

                  // Send download request to Flutter
                  window.MediaDownloader.postMessage(JSON.stringify({
                    url: src,
                    fileName: fileName,
                    type: fileType
                  }));
                }
              };

              // Position the element relatively and add the button
              const wrapper = document.createElement('div');
              wrapper.style.position = 'relative';
              wrapper.style.display = 'inline-block';

              element.parentNode.insertBefore(wrapper, element);
              wrapper.appendChild(element);
              wrapper.appendChild(downloadBtn);
            }
          });

          // Also handle direct links to media files
          const links = document.querySelectorAll('a[href]');
          links.forEach(function(link) {
            const href = link.href;
            if (href.match(/\\.(mp4|avi|mov|wmv|flv|webm|mp3|wav|ogg|aac|m4a)\$/i)) {
              if (!link.hasAttribute('data-download-added')) {
                link.setAttribute('data-download-added', 'true');

                // Add download option to context menu or click handler
                link.addEventListener('contextmenu', function(e) {
                  e.preventDefault();

                  const fileName = href.split('/').pop() || 'media_file';
                  const fileType = href.match(/\\.(mp4|avi|mov|wmv|flv|webm)\$/i) ? 'video' : 'audio';

                  window.MediaDownloader.postMessage(JSON.stringify({
                    url: href,
                    fileName: fileName,
                    type: fileType
                  }));
                });
              }
            }
          });
        }

        // Function to setup media picker for "Add media" buttons
        function setupMediaPicker() {
          // Look for various types of upload/media buttons and inputs
          const selectors = [
            'button',
            '[role="button"]',
            'input[type="file"]',
            '.upload-btn',
            '.add-media',
            '.file-upload',
            '[data-testid*="upload"]',
            '[data-testid*="file"]',
            '[data-testid*="media"]'
          ];

          const elements = document.querySelectorAll(selectors.join(', '));

          elements.forEach(function(element) {
            if (element.hasAttribute('data-picker-added')) return;

            const text = (element.textContent || element.innerText || element.value || '').toLowerCase();
            const className = (element.className || '').toLowerCase();
            const id = (element.id || '').toLowerCase();
            const dataTestId = (element.getAttribute('data-testid') || '').toLowerCase();

            // More comprehensive detection
            const isMediaButton =
              text.includes('add media') ||
              text.includes('upload') ||
              text.includes('choose file') ||
              text.includes('select file') ||
              text.includes('browse') ||
              text.includes('attach') ||
              className.includes('upload') ||
              className.includes('file') ||
              className.includes('media') ||
              id.includes('upload') ||
              id.includes('file') ||
              id.includes('media') ||
              dataTestId.includes('upload') ||
              dataTestId.includes('file') ||
              dataTestId.includes('media') ||
              element.tagName.toLowerCase() === 'input' && element.type === 'file';

            if (isMediaButton) {
              element.setAttribute('data-picker-added', 'true');

              console.log('Found media button:', element);

              // For file inputs, override the click
              if (element.tagName.toLowerCase() === 'input' && element.type === 'file') {
                element.addEventListener('click', function(e) {
                  e.preventDefault();
                  e.stopPropagation();

                  console.log('File input clicked, opening Flutter picker');

                  window.MediaPicker.postMessage(JSON.stringify({
                    action: 'pick_media',
                    elementType: 'file_input',
                    accept: element.accept || '*/*'
                  }));
                });
              } else {
                // For buttons and other elements
                element.addEventListener('click', function(e) {
                  e.preventDefault();
                  e.stopPropagation();

                  console.log('Media button clicked, opening Flutter picker');

                  window.MediaPicker.postMessage(JSON.stringify({
                    action: 'pick_media',
                    elementType: 'button',
                    buttonText: text,
                    buttonId: id,
                    buttonClass: className
                  }));
                });
              }
            }
          });

          // Also look for drag and drop areas
          const dropAreas = document.querySelectorAll('[data-testid*="drop"], .drop-zone, .upload-area, .file-drop');
          dropAreas.forEach(function(area) {
            if (!area.hasAttribute('data-drop-added')) {
              area.setAttribute('data-drop-added', 'true');

              area.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                console.log('Drop area clicked, opening Flutter picker');

                window.MediaPicker.postMessage(JSON.stringify({
                  action: 'pick_media',
                  elementType: 'drop_area'
                }));
              });
            }
          });
        }

        // Run initially
        setupMediaDownloader();
        setupMediaPicker();

        // Run when new content is loaded (for dynamic content)
        const observer = new MutationObserver(function(mutations) {
          mutations.forEach(function(mutation) {
            if (mutation.addedNodes.length > 0) {
              setupMediaDownloader();
              setupMediaPicker();
            }
          });
        });

        observer.observe(document.body, {
          childList: true,
          subtree: true
        });
      })();
    ''';

    _webViewController.runJavaScript(script);
  }

  void _handleDownloadRequest(String message) async {
    try {
      LogService.log.i('Download request received: $message');

      // Parse JSON message from JavaScript
      if (message.contains('{') && message.contains('}')) {
        // Simple JSON parsing for the expected format
        final urlMatch = RegExp(r'"url":"([^"]*)"').firstMatch(message);
        final fileNameMatch =
            RegExp(r'"fileName":"([^"]*)"').firstMatch(message);
        final typeMatch = RegExp(r'"type":"([^"]*)"').firstMatch(message);

        if (urlMatch != null) {
          final url = urlMatch.group(1) ?? '';
          final fileName = fileNameMatch?.group(1) ?? 'media_file';
          final fileType = typeMatch?.group(1) ?? 'unknown';

          await _downloadFile(url, fileName, fileType);
        }
      }
    } catch (e) {
      LogService.log.e('Error handling download: $e');
      if (mounted) {
        SnackbarUtil.showOnce(
          title: "Error",
          message: "Failed to download file",
        );
      }
    }
  }

  void _handleMediaPickerRequest(String message) async {
    try {
      LogService.log.i('Media picker request received: $message');

      // Parse JSON message from JavaScript
      if (message.contains('pick_media')) {
        await _showMediaPickerDialog();
      }
    } catch (e) {
      LogService.log.e('Error handling media picker: $e');
      if (mounted) {
        SnackbarUtil.showOnce(
          title: "Error",
          message: "Failed to open media picker",
        );
      }
    }
  }

  Future<void> _showMediaPickerDialog() async {
    if (!mounted) return;

    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return Container(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                'Select Media Type',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 20),
              ListTile(
                leading: const Icon(Icons.video_library),
                title: const Text('Pick Video'),
                onTap: () async {
                  Navigator.pop(context);
                  await _pickVideo();
                },
              ),
              ListTile(
                leading: const Icon(Icons.photo_library),
                title: const Text('Pick Image'),
                onTap: () async {
                  Navigator.pop(context);
                  await _pickImage();
                },
              ),
              ListTile(
                leading: const Icon(Icons.audiotrack),
                title: const Text('Pick Audio'),
                onTap: () async {
                  Navigator.pop(context);
                  await _pickAudio();
                },
              ),
              ListTile(
                leading: const Icon(Icons.file_present),
                title: const Text('Pick Any File'),
                onTap: () async {
                  Navigator.pop(context);
                  await _pickAnyFile();
                },
              ),
            ],
          ),
        );
      },
    );
  }

  Future<void> _pickVideo() async {
    try {
      final String? path = await FilePickerUtil.pickVideo();
      if (path != null) {
        await _uploadFileToWebView(path, 'video');
      }
    } catch (e) {
      LogService.log.e('Error picking video: $e');
      if (mounted) {
        SnackbarUtil.showOnce(
          title: "Error",
          message: "Failed to pick video",
        );
      }
    }
  }

  Future<void> _pickImage() async {
    try {
      final String? path = await FilePickerUtil.pickImage();
      if (path != null) {
        await _uploadFileToWebView(path, 'image');
      }
    } catch (e) {
      LogService.log.e('Error picking image: $e');
      if (mounted) {
        SnackbarUtil.showOnce(
          title: "Error",
          message: "Failed to pick image",
        );
      }
    }
  }

  Future<void> _pickAudio() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.audio,
        allowMultiple: false,
      );

      if (result != null && result.files.isNotEmpty) {
        final path = result.files.first.path;
        if (path != null) {
          await _uploadFileToWebView(path, 'audio');
        }
      }
    } catch (e) {
      LogService.log.e('Error picking audio: $e');
      if (mounted) {
        SnackbarUtil.showOnce(
          title: "Error",
          message: "Failed to pick audio",
        );
      }
    }
  }

  Future<void> _pickAnyFile() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        allowMultiple: false,
      );

      if (result != null && result.files.isNotEmpty) {
        final path = result.files.first.path;
        if (path != null) {
          await _uploadFileToWebView(path, 'file');
        }
      }
    } catch (e) {
      LogService.log.e('Error picking file: $e');
      if (mounted) {
        SnackbarUtil.showOnce(
          title: "Error",
          message: "Failed to pick file",
        );
      }
    }
  }

  Future<void> _uploadFileToWebView(String filePath, String fileType) async {
    try {
      LogService.log.i('Uploading file to WebView: $filePath');

      // Read file as base64
      final file = File(filePath);
      final bytes = await file.readAsBytes();
      final base64String = base64Encode(bytes);
      final fileName = file.path.split('/').last;
      final mimeType = _getMimeType(fileName);

      // Create data URL
      final dataUrl = 'data:$mimeType;base64,$base64String';

      // Inject JavaScript to handle the file upload
      final script = '''
        (function() {
          try {
            // Create a file object from the data URL
            const fileName = '$fileName';
            const fileType = '$fileType';
            const dataUrl = '$dataUrl';

            // Convert data URL to blob
            fetch(dataUrl)
              .then(res => res.blob())
              .then(blob => {
                // Create a File object
                const file = new File([blob], fileName, { type: '$mimeType' });

                // Try to find file input elements and set the file
                const fileInputs = document.querySelectorAll('input[type="file"]');
                if (fileInputs.length > 0) {
                  // Create a DataTransfer object to simulate file selection
                  const dt = new DataTransfer();
                  dt.items.add(file);

                  fileInputs.forEach(input => {
                    input.files = dt.files;
                    // Trigger change event
                    const event = new Event('change', { bubbles: true });
                    input.dispatchEvent(event);
                  });
                }

                // Also try to trigger any drag and drop handlers
                const dropZones = document.querySelectorAll('[data-testid*="drop"], .drop-zone, .upload-area');
                if (dropZones.length > 0) {
                  dropZones.forEach(zone => {
                    const dt = new DataTransfer();
                    dt.items.add(file);

                    const dropEvent = new DragEvent('drop', {
                      bubbles: true,
                      dataTransfer: dt
                    });
                    zone.dispatchEvent(dropEvent);
                  });
                }

                // Try to find and trigger upload buttons or areas
                const uploadButtons = document.querySelectorAll('button, [role="button"]');
                uploadButtons.forEach(button => {
                  const text = button.textContent || button.innerText || '';
                  if (text.toLowerCase().includes('upload') ||
                      text.toLowerCase().includes('add') ||
                      text.toLowerCase().includes('choose')) {
                    // Store file data on the button for later use
                    button._selectedFile = file;
                  }
                });

                console.log('File uploaded successfully:', fileName);
              })
              .catch(error => {
                console.error('Error uploading file:', error);
              });
          } catch (error) {
            console.error('Error in upload script:', error);
          }
        })();
      ''';

      await _webViewController.runJavaScript(script);

      if (mounted) {
        SnackbarUtil.showOnce(
          title: "Success",
          message: "File $fileName uploaded successfully",
        );
      }
    } catch (e) {
      LogService.log.e('Error uploading file to WebView: $e');
      if (mounted) {
        SnackbarUtil.showOnce(
          title: "Error",
          message: "Failed to upload file",
        );
      }
    }
  }

  String _getMimeType(String fileName) {
    final extension = fileName.split('.').last.toLowerCase();
    switch (extension) {
      case 'mp4':
        return 'video/mp4';
      case 'avi':
        return 'video/avi';
      case 'mov':
        return 'video/quicktime';
      case 'wmv':
        return 'video/x-ms-wmv';
      case 'flv':
        return 'video/x-flv';
      case 'webm':
        return 'video/webm';
      case 'mp3':
        return 'audio/mpeg';
      case 'wav':
        return 'audio/wav';
      case 'ogg':
        return 'audio/ogg';
      case 'aac':
        return 'audio/aac';
      case 'm4a':
        return 'audio/mp4';
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      case 'png':
        return 'image/png';
      case 'gif':
        return 'image/gif';
      case 'webp':
        return 'image/webp';
      case 'pdf':
        return 'application/pdf';
      case 'doc':
        return 'application/msword';
      case 'docx':
        return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
      default:
        return 'application/octet-stream';
    }
  }

  Future<void> _downloadFile(
      String url, String fileName, String fileType) async {
    try {
      // Request storage permission
      final permission = await Permission.storage.request();
      if (permission.isGranted || await Permission.photos.request().isGranted) {
        // Show loading indicator
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Downloading file...'),
              duration: Duration(seconds: 2),
            ),
          );
        }

        // Download the file
        await FileDownloadUtil.downloadFile(
          url,
          fileName: fileName,
        );

        if (mounted) {
          SnackbarUtil.showOnce(
            title: "Success",
            message: "File $fileType downloaded to gallery",
          );
        }
      } else {
        if (mounted) {
          SnackbarUtil.showOnce(
            title: "Permission Required",
            message: "Storage permission is required to download files",
          );
        }
      }
    } catch (e) {
      LogService.log.e('Error downloading file: $e');
      if (mounted) {
        SnackbarUtil.showOnce(
          title: "Error",
          message: "Failed to download file",
        );
      }
    }
  }

  @override
  void dispose() {
    // Clean up WebView resources
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Video Editor'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              _webViewController.reload();
            },
          ),
        ],
      ),
      body: Stack(
        children: [
          WebViewWidget(controller: _webViewController),
          if (_isLoading)
            const Center(
              child: CircularProgressIndicator(),
            ),
        ],
      ),
    );
  }
}
